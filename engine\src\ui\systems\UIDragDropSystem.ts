/**
 * UIDragDropSystem.ts
 *
 * UI拖拽系统，提供拖拽功能支持
 */

import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import { UIComponent } from '../components/UIComponent';
import { Vector2 } from 'three';

/**
 * 拖拽事件类型
 */
export enum DragEventType {
  DRAG_START = 'dragStart',
  DRAG_MOVE = 'dragMove',
  DRAG_END = 'dragEnd',
  DROP = 'drop',
  DRAG_ENTER = 'dragEnter',
  DRAG_LEAVE = 'dragLeave',
  DRAG_OVER = 'dragOver'
}

/**
 * 拖拽数据接口
 */
export interface DragData {
  component: UIComponent;
  entity: Entity;
  startPosition: Vector2;
  currentPosition: Vector2;
  offset: Vector2;
  data?: any;
}

/**
 * 拖拽配置接口
 */
export interface DragConfig {
  // 是否可拖拽
  draggable?: boolean;
  
  // 是否可放置
  droppable?: boolean;
  
  // 拖拽手柄选择器
  handle?: string;
  
  // 拖拽约束
  constraint?: 'horizontal' | 'vertical' | 'none';
  
  // 拖拽边界
  bounds?: {
    left?: number;
    top?: number;
    right?: number;
    bottom?: number;
  };
  
  // 拖拽时的样式类
  dragClass?: string;
  
  // 放置时的样式类
  dropClass?: string;
  
  // 拖拽数据
  dragData?: any;
  
  // 事件回调
  onDragStart?: (data: DragData) => boolean;
  onDragMove?: (data: DragData) => void;
  onDragEnd?: (data: DragData) => void;
  onDrop?: (dragData: DragData, dropTarget: UIComponent) => void;
  onDragEnter?: (dragData: DragData, dropTarget: UIComponent) => void;
  onDragLeave?: (dragData: DragData, dropTarget: UIComponent) => void;
  onDragOver?: (dragData: DragData, dropTarget: UIComponent) => boolean;
}

/**
 * UI拖拽系统
 */
export class UIDragDropSystem extends System {
  // 拖拽配置映射
  private dragConfigs: Map<UIComponent, DragConfig> = new Map();
  
  // 当前拖拽状态
  private currentDrag?: DragData;
  private isDragging: boolean = false;
  
  // 拖拽元素
  private dragElement?: HTMLElement;
  private dragGhost?: HTMLElement;
  
  // 事件监听器
  private boundMouseMove = this.handleMouseMove.bind(this);
  private boundMouseUp = this.handleMouseUp.bind(this);
  private boundTouchMove = this.handleTouchMove.bind(this);
  private boundTouchEnd = this.handleTouchEnd.bind(this);

  /**
   * 构造函数
   */
  constructor() {
    super(500); // 较高优先级
  }

  /**
   * 注册可拖拽组件
   * @param component UI组件
   * @param config 拖拽配置
   */
  registerDraggable(component: UIComponent, config: DragConfig): void {
    this.dragConfigs.set(component, config);
    
    if (config.draggable && component.htmlElement) {
      this.setupDragEvents(component);
    }
  }

  /**
   * 注销拖拽组件
   * @param component UI组件
   */
  unregisterDraggable(component: UIComponent): void {
    this.dragConfigs.delete(component);
    this.removeDragEvents(component);
  }

  /**
   * 设置拖拽事件
   * @param component UI组件
   */
  private setupDragEvents(component: UIComponent): void {
    if (!component.htmlElement) return;
    
    const config = this.dragConfigs.get(component);
    if (!config) return;
    
    const element = config.handle ? 
      component.htmlElement.querySelector(config.handle) as HTMLElement :
      component.htmlElement;
    
    if (!element) return;
    
    // 鼠标事件
    element.addEventListener('mousedown', (event) => {
      this.handleDragStart(event, component);
    });
    
    // 触摸事件
    element.addEventListener('touchstart', (event) => {
      this.handleTouchStart(event, component);
    }, { passive: false });
    
    // 设置样式
    element.style.cursor = 'grab';
  }

  /**
   * 移除拖拽事件
   * @param component UI组件
   */
  private removeDragEvents(component: UIComponent): void {
    if (!component.htmlElement) return;
    
    const config = this.dragConfigs.get(component);
    if (!config) return;
    
    const element = config.handle ? 
      component.htmlElement.querySelector(config.handle) as HTMLElement :
      component.htmlElement;
    
    if (!element) return;
    
    // 移除事件监听器
    element.removeEventListener('mousedown', this.handleDragStart);
    element.removeEventListener('touchstart', this.handleTouchStart);
    
    // 重置样式
    element.style.cursor = '';
  }

  /**
   * 处理拖拽开始
   * @param event 鼠标事件
   * @param component UI组件
   */
  private handleDragStart(event: MouseEvent, component: UIComponent): void {
    const config = this.dragConfigs.get(component);
    if (!config || !config.draggable) return;
    
    const startPosition = new Vector2(event.clientX, event.clientY);
    const rect = component.htmlElement!.getBoundingClientRect();
    const offset = new Vector2(
      event.clientX - rect.left,
      event.clientY - rect.top
    );
    
    const dragData: DragData = {
      component,
      entity: component.entity,
      startPosition,
      currentPosition: startPosition.clone(),
      offset,
      data: config.dragData
    };
    
    // 调用开始回调
    if (config.onDragStart && !config.onDragStart(dragData)) {
      return; // 取消拖拽
    }
    
    this.startDrag(dragData, event);
    event.preventDefault();
  }

  /**
   * 处理触摸开始
   * @param event 触摸事件
   * @param component UI组件
   */
  private handleTouchStart(event: TouchEvent, component: UIComponent): void {
    if (event.touches.length !== 1) return;
    
    const touch = event.touches[0];
    const config = this.dragConfigs.get(component);
    if (!config || !config.draggable) return;
    
    const startPosition = new Vector2(touch.clientX, touch.clientY);
    const rect = component.htmlElement!.getBoundingClientRect();
    const offset = new Vector2(
      touch.clientX - rect.left,
      touch.clientY - rect.top
    );
    
    const dragData: DragData = {
      component,
      entity: component.entity,
      startPosition,
      currentPosition: startPosition.clone(),
      offset,
      data: config.dragData
    };
    
    // 调用开始回调
    if (config.onDragStart && !config.onDragStart(dragData)) {
      return; // 取消拖拽
    }
    
    this.startDrag(dragData, touch);
    event.preventDefault();
  }

  /**
   * 开始拖拽
   * @param dragData 拖拽数据
   * @param event 事件对象
   */
  private startDrag(dragData: DragData, event: MouseEvent | Touch): void {
    this.currentDrag = dragData;
    this.isDragging = true;
    this.dragElement = dragData.component.htmlElement!;
    
    const config = this.dragConfigs.get(dragData.component)!;
    
    // 添加拖拽样式
    if (config.dragClass) {
      this.dragElement.classList.add(config.dragClass);
    }
    
    // 创建拖拽幽灵元素
    this.createDragGhost();
    
    // 设置样式
    this.dragElement.style.cursor = 'grabbing';
    
    // 添加全局事件监听器
    if (event instanceof MouseEvent) {
      document.addEventListener('mousemove', this.boundMouseMove);
      document.addEventListener('mouseup', this.boundMouseUp);
    } else {
      document.addEventListener('touchmove', this.boundTouchMove, { passive: false });
      document.addEventListener('touchend', this.boundTouchEnd);
    }
    
    // 触发拖拽开始事件
    this.emit(DragEventType.DRAG_START, dragData);
  }

  /**
   * 创建拖拽幽灵元素
   */
  private createDragGhost(): void {
    if (!this.dragElement) return;
    
    this.dragGhost = this.dragElement.cloneNode(true) as HTMLElement;
    this.dragGhost.style.position = 'fixed';
    this.dragGhost.style.pointerEvents = 'none';
    this.dragGhost.style.opacity = '0.7';
    this.dragGhost.style.zIndex = '9999';
    this.dragGhost.style.transform = 'rotate(5deg)';
    
    document.body.appendChild(this.dragGhost);
  }

  /**
   * 处理鼠标移动
   * @param event 鼠标事件
   */
  private handleMouseMove(event: MouseEvent): void {
    if (!this.isDragging || !this.currentDrag) return;
    
    this.updateDragPosition(new Vector2(event.clientX, event.clientY));
    event.preventDefault();
  }

  /**
   * 处理触摸移动
   * @param event 触摸事件
   */
  private handleTouchMove(event: TouchEvent): void {
    if (!this.isDragging || !this.currentDrag || event.touches.length !== 1) return;
    
    const touch = event.touches[0];
    this.updateDragPosition(new Vector2(touch.clientX, touch.clientY));
    event.preventDefault();
  }

  /**
   * 更新拖拽位置
   * @param position 当前位置
   */
  private updateDragPosition(position: Vector2): void {
    if (!this.currentDrag || !this.dragElement) return;
    
    this.currentDrag.currentPosition = position;
    const config = this.dragConfigs.get(this.currentDrag.component)!;
    
    // 计算新位置
    let newX = position.x - this.currentDrag.offset.x;
    let newY = position.y - this.currentDrag.offset.y;
    
    // 应用约束
    if (config.constraint === 'horizontal') {
      newY = this.currentDrag.startPosition.y - this.currentDrag.offset.y;
    } else if (config.constraint === 'vertical') {
      newX = this.currentDrag.startPosition.x - this.currentDrag.offset.x;
    }
    
    // 应用边界约束
    if (config.bounds) {
      if (config.bounds.left !== undefined) {
        newX = Math.max(newX, config.bounds.left);
      }
      if (config.bounds.top !== undefined) {
        newY = Math.max(newY, config.bounds.top);
      }
      if (config.bounds.right !== undefined) {
        newX = Math.min(newX, config.bounds.right - this.dragElement.offsetWidth);
      }
      if (config.bounds.bottom !== undefined) {
        newY = Math.min(newY, config.bounds.bottom - this.dragElement.offsetHeight);
      }
    }
    
    // 更新元素位置
    this.dragElement.style.left = `${newX}px`;
    this.dragElement.style.top = `${newY}px`;
    
    // 更新幽灵元素位置
    if (this.dragGhost) {
      this.dragGhost.style.left = `${position.x - this.currentDrag.offset.x}px`;
      this.dragGhost.style.top = `${position.y - this.currentDrag.offset.y}px`;
    }
    
    // 检查放置目标
    this.checkDropTargets(position);
    
    // 调用移动回调
    if (config.onDragMove) {
      config.onDragMove(this.currentDrag);
    }
    
    // 触发拖拽移动事件
    this.emit(DragEventType.DRAG_MOVE, this.currentDrag);
  }

  /**
   * 检查放置目标
   * @param position 当前位置
   */
  private checkDropTargets(position: Vector2): void {
    if (!this.currentDrag) return;
    
    // 获取当前位置下的元素
    const elementBelow = document.elementFromPoint(position.x, position.y);
    if (!elementBelow) return;
    
    // 查找可放置的组件
    for (const [component, config] of this.dragConfigs) {
      if (!config.droppable || component === this.currentDrag.component) continue;
      
      if (component.htmlElement && component.htmlElement.contains(elementBelow)) {
        // 触发拖拽悬停事件
        if (config.onDragOver && !config.onDragOver(this.currentDrag, component)) {
          continue;
        }
        
        this.emit(DragEventType.DRAG_OVER, this.currentDrag, component);
        break;
      }
    }
  }

  /**
   * 处理鼠标释放
   * @param event 鼠标事件
   */
  private handleMouseUp(event: MouseEvent): void {
    if (!this.isDragging) return;
    
    this.endDrag(new Vector2(event.clientX, event.clientY));
    
    // 移除事件监听器
    document.removeEventListener('mousemove', this.boundMouseMove);
    document.removeEventListener('mouseup', this.boundMouseUp);
  }

  /**
   * 处理触摸结束
   * @param event 触摸事件
   */
  private handleTouchEnd(event: TouchEvent): void {
    if (!this.isDragging) return;
    
    const touch = event.changedTouches[0];
    this.endDrag(new Vector2(touch.clientX, touch.clientY));
    
    // 移除事件监听器
    document.removeEventListener('touchmove', this.boundTouchMove);
    document.removeEventListener('touchend', this.boundTouchEnd);
  }

  /**
   * 结束拖拽
   * @param position 结束位置
   */
  private endDrag(position: Vector2): void {
    if (!this.currentDrag || !this.dragElement) return;
    
    const config = this.dragConfigs.get(this.currentDrag.component)!;
    
    // 移除拖拽样式
    if (config.dragClass) {
      this.dragElement.classList.remove(config.dragClass);
    }
    
    // 重置样式
    this.dragElement.style.cursor = 'grab';
    
    // 移除幽灵元素
    if (this.dragGhost) {
      document.body.removeChild(this.dragGhost);
      this.dragGhost = undefined;
    }
    
    // 检查放置
    this.checkDrop(position);
    
    // 调用结束回调
    if (config.onDragEnd) {
      config.onDragEnd(this.currentDrag);
    }
    
    // 触发拖拽结束事件
    this.emit(DragEventType.DRAG_END, this.currentDrag);
    
    // 重置状态
    this.currentDrag = undefined;
    this.isDragging = false;
    this.dragElement = undefined;
  }

  /**
   * 检查放置
   * @param position 放置位置
   */
  private checkDrop(position: Vector2): void {
    if (!this.currentDrag) return;
    
    // 获取放置位置下的元素
    const elementBelow = document.elementFromPoint(position.x, position.y);
    if (!elementBelow) return;
    
    // 查找可放置的组件
    for (const [component, config] of this.dragConfigs) {
      if (!config.droppable || component === this.currentDrag.component) continue;
      
      if (component.htmlElement && component.htmlElement.contains(elementBelow)) {
        // 调用放置回调
        if (config.onDrop) {
          config.onDrop(this.currentDrag, component);
        }
        
        // 触发放置事件
        this.emit(DragEventType.DROP, this.currentDrag, component);
        break;
      }
    }
  }

  /**
   * 更新系统
   * @param deltaTime 时间增量
   */
  update(deltaTime: number): void {
    // 拖拽系统不需要定期更新
  }

  /**
   * 销毁系统
   */
  dispose(): void {
    // 清理所有拖拽配置
    for (const component of this.dragConfigs.keys()) {
      this.unregisterDraggable(component);
    }
    
    this.dragConfigs.clear();
    
    // 如果正在拖拽，结束拖拽
    if (this.isDragging && this.currentDrag) {
      this.endDrag(this.currentDrag.currentPosition);
    }
    
    super.dispose();
  }
}
