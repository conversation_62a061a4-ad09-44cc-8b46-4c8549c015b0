/**
 * UI2DSystem.ts
 *
 * 2D UI系统，管理2D界面元素
 */

import { System } from '../../core/System';
import type { World } from '../../core/World';
import type { Entity } from '../../core/Entity';
import { UI2DComponent, UI2DComponentProps } from '../components/UI2DComponent';
import { UIComponentType } from '../components/UIComponent';
import { UIProgressBarComponent, UIProgressBarComponentProps } from '../components/UIProgressBarComponent';
import { UITooltipComponent, UITooltipComponentProps } from '../components/UITooltipComponent';
import { UIModalComponent, UIModalComponentProps } from '../components/UIModalComponent';
import { UISystem } from './UISystem';

/**
 * 2D UI系统配置
 */
export interface UI2DSystemConfig {
  // 是否启用调试模式
  debug?: boolean;

  // 是否自动创建HTML容器
  autoCreateContainer?: boolean;

  // HTML容器ID
  containerId?: string;

  // 默认字体
  defaultFont?: string;

  // 默认字体大小
  defaultFontSize?: number;

  // 默认文本颜色
  defaultTextColor?: string;

  // 默认背景颜色
  defaultBackgroundColor?: string;

  // 默认边框颜色
  defaultBorderColor?: string;

  // 默认边框宽度
  defaultBorderWidth?: number;

  // 默认边框圆角
  defaultBorderRadius?: number;

  // 默认内边距
  defaultPadding?: number;

  // 默认外边距
  defaultMargin?: number;
}

/**
 * 2D UI系统
 * 管理2D界面元素
 */
export class UI2DSystem extends System {
  // UI系统引用
  private uiSystem: UISystem;

  // 配置
  private config: UI2DSystemConfig;

  // HTML容器元素
  private container?: HTMLElement;

  /**
   * 构造函数
   * @param world 世界实例
   * @param uiSystem UI系统实例
   * @param config 2D UI系统配置
   */
  constructor(world: World, uiSystem: UISystem, config: UI2DSystemConfig = {}) {
    // 调用基类构造函数，传入优先级（默认为0）
    super(0);

    // 设置世界引用（使用基类方法）
    this.setWorld(world);

    this.uiSystem = uiSystem;

    this.config = {
      debug: config.debug || false,
      autoCreateContainer: config.autoCreateContainer !== undefined ? config.autoCreateContainer : true,
      containerId: config.containerId || 'ui-2d-container',
      defaultFont: config.defaultFont || 'Arial, sans-serif',
      defaultFontSize: config.defaultFontSize || 16,
      defaultTextColor: config.defaultTextColor || '#000000',
      defaultBackgroundColor: config.defaultBackgroundColor || 'transparent',
      defaultBorderColor: config.defaultBorderColor || 'transparent',
      defaultBorderWidth: config.defaultBorderWidth || 0,
      defaultBorderRadius: config.defaultBorderRadius || 0,
      defaultPadding: config.defaultPadding || 0,
      defaultMargin: config.defaultMargin || 0
    };

    // 如果自动创建容器
    if (this.config.autoCreateContainer) {
      this.createContainer();
    }
  }

  /**
   * 创建HTML容器
   */
  private createContainer(): void {
    // 检查容器是否已存在
    let container = document.getElementById(this.config.containerId!);

    // 如果不存在，则创建
    if (!container) {
      container = document.createElement('div');
      container.id = this.config.containerId!;
      container.style.position = 'absolute';
      container.style.top = '0';
      container.style.left = '0';
      container.style.width = '100%';
      container.style.height = '100%';
      container.style.pointerEvents = 'none';
      container.style.overflow = 'hidden';
      container.style.zIndex = '1000';

      document.body.appendChild(container);
    }

    this.container = container;
  }

  /**
   * 创建2D UI元素
   * @param entity 实体
   * @param type UI元素类型
   * @param props UI元素属性
   * @returns 创建的2D UI组件
   */
  createUIElement(entity: Entity, type: UIComponentType, props: UI2DComponentProps = {}): UI2DComponent {
    // 合并默认属性和提供的属性
    const mergedProps: UI2DComponentProps = {
      type,
      fontFamily: this.config.defaultFont,
      fontSize: this.config.defaultFontSize,
      textColor: this.config.defaultTextColor,
      backgroundColor: this.config.defaultBackgroundColor,
      borderColor: this.config.defaultBorderColor,
      borderWidth: this.config.defaultBorderWidth,
      borderRadius: this.config.defaultBorderRadius,
      padding: this.config.defaultPadding,
      margin: this.config.defaultMargin,
      ...props
    };

    // 创建2D UI组件
    const component = new UI2DComponent(mergedProps);

    // 注册到UI系统
    this.uiSystem.registerUIComponent(entity, component);

    // 如果有容器，将HTML元素添加到容器
    if (this.container && component.htmlElement && !component.htmlElement.parentElement) {
      this.container.appendChild(component.htmlElement);
    }

    return component;
  }

  /**
   * 创建按钮
   * @param entity 实体
   * @param text 按钮文本
   * @param props 按钮属性
   * @returns 创建的按钮组件
   */
  createButton(entity: Entity, text: string, props: UI2DComponentProps = {}): UI2DComponent {
    return this.createUIElement(entity, UIComponentType.BUTTON, {
      textContent: text,
      backgroundColor: props.backgroundColor || '#f0f0f0',
      borderColor: props.borderColor || '#cccccc',
      borderWidth: props.borderWidth || 1,
      borderRadius: props.borderRadius || 4,
      padding: props.padding || 8,
      ...props
    });
  }

  /**
   * 创建文本
   * @param entity 实体
   * @param text 文本内容
   * @param props 文本属性
   * @returns 创建的文本组件
   */
  createText(entity: Entity, text: string, props: UI2DComponentProps = {}): UI2DComponent {
    return this.createUIElement(entity, UIComponentType.TEXT, {
      textContent: text,
      ...props
    });
  }

  /**
   * 创建图像
   * @param entity 实体
   * @param src 图像源
   * @param props 图像属性
   * @returns 创建的图像组件
   */
  createImage(entity: Entity, src: string, props: UI2DComponentProps = {}): UI2DComponent {
    const component = this.createUIElement(entity, UIComponentType.IMAGE, props);

    // 设置图像源
    if (component.htmlElement instanceof HTMLImageElement) {
      component.htmlElement.src = src;
    }

    return component;
  }

  /**
   * 创建输入框
   * @param entity 实体
   * @param placeholder 占位文本
   * @param props 输入框属性
   * @returns 创建的输入框组件
   */
  createInput(entity: Entity, placeholder: string = '', props: UI2DComponentProps = {}): UI2DComponent {
    const component = this.createUIElement(entity, UIComponentType.INPUT, {
      backgroundColor: props.backgroundColor || '#ffffff',
      borderColor: props.borderColor || '#cccccc',
      borderWidth: props.borderWidth || 1,
      borderRadius: props.borderRadius || 4,
      padding: props.padding || 8,
      ...props
    });

    // 设置占位文本
    if (component.htmlElement instanceof HTMLInputElement) {
      component.htmlElement.placeholder = placeholder;
    }

    return component;
  }

  /**
   * 创建复选框
   * @param entity 实体
   * @param label 标签文本
   * @param checked 是否选中
   * @param props 复选框属性
   * @returns 创建的复选框组件
   */
  createCheckbox(entity: Entity, label: string = '', checked: boolean = false, props: UI2DComponentProps = {}): UI2DComponent {
    const component = this.createUIElement(entity, UIComponentType.CHECKBOX, props);

    // 设置选中状态
    if (component.htmlElement instanceof HTMLInputElement) {
      component.htmlElement.checked = checked;

      // 创建标签
      if (label) {
        const labelElement = document.createElement('label');
        labelElement.textContent = label;
        labelElement.style.marginLeft = '5px';

        // 创建容器
        const container = document.createElement('div');
        container.style.display = 'flex';
        container.style.alignItems = 'center';

        // 将复选框和标签添加到容器
        container.appendChild(component.htmlElement);
        container.appendChild(labelElement);

        // 替换原始元素
        if (component.htmlElement.parentElement) {
          component.htmlElement.parentElement.replaceChild(container, component.htmlElement);
        }

        // 更新组件的HTML元素
        component.htmlElement = container;
      }
    }

    return component;
  }

  /**
   * 创建滑块
   * @param entity 实体
   * @param min 最小值
   * @param max 最大值
   * @param value 当前值
   * @param props 滑块属性
   * @returns 创建的滑块组件
   */
  createSlider(entity: Entity, min: number = 0, max: number = 100, value: number = 50, props: UI2DComponentProps = {}): UI2DComponent {
    const component = this.createUIElement(entity, UIComponentType.SLIDER, props);

    // 设置滑块属性
    if (component.htmlElement instanceof HTMLInputElement) {
      component.htmlElement.min = min.toString();
      component.htmlElement.max = max.toString();
      component.htmlElement.value = value.toString();
    }

    return component;
  }

  /**
   * 创建下拉框
   * @param entity 实体
   * @param options 选项列表
   * @param selectedIndex 选中项索引
   * @param props 下拉框属性
   * @returns 创建的下拉框组件
   */
  createDropdown(entity: Entity, options: string[] = [], selectedIndex: number = 0, props: UI2DComponentProps = {}): UI2DComponent {
    const component = this.createUIElement(entity, UIComponentType.DROPDOWN, {
      backgroundColor: props.backgroundColor || '#ffffff',
      borderColor: props.borderColor || '#cccccc',
      borderWidth: props.borderWidth || 1,
      borderRadius: props.borderRadius || 4,
      padding: props.padding || 8,
      ...props
    });

    // 添加选项
    if (component.htmlElement instanceof HTMLSelectElement) {
      options.forEach((optionText, index) => {
        const option = document.createElement('option');
        option.value = index.toString();
        option.text = optionText;
        option.selected = index === selectedIndex;
        component.htmlElement?.appendChild(option);
      });
    }

    return component;
  }

  /**
   * 创建面板
   * @param entity 实体
   * @param props 面板属性
   * @returns 创建的面板组件
   */
  createPanel(entity: Entity, props: UI2DComponentProps = {}): UI2DComponent {
    return this.createUIElement(entity, UIComponentType.PANEL, {
      backgroundColor: props.backgroundColor || '#ffffff',
      borderColor: props.borderColor || '#cccccc',
      borderWidth: props.borderWidth || 1,
      borderRadius: props.borderRadius || 4,
      padding: props.padding || 16,
      ...props
    });
  }

  /**
   * 创建窗口
   * @param entity 实体
   * @param title 窗口标题
   * @param props 窗口属性
   * @returns 创建的窗口组件
   */
  createWindow(entity: Entity, title: string = '', props: UI2DComponentProps = {}): UI2DComponent {
    const component = this.createUIElement(entity, UIComponentType.WINDOW, {
      backgroundColor: props.backgroundColor || '#ffffff',
      borderColor: props.borderColor || '#cccccc',
      borderWidth: props.borderWidth || 1,
      borderRadius: props.borderRadius || 4,
      ...props
    });

    // 如果有标题，创建标题栏
    if (title && component.htmlElement) {
      // 创建标题栏
      const titleBar = document.createElement('div');
      titleBar.style.padding = '8px';
      titleBar.style.backgroundColor = '#f0f0f0';
      titleBar.style.borderBottom = '1px solid #cccccc';
      titleBar.style.borderTopLeftRadius = `${component.borderRadius}px`;
      titleBar.style.borderTopRightRadius = `${component.borderRadius}px`;
      titleBar.style.fontWeight = 'bold';
      titleBar.textContent = title;

      // 创建内容区域
      const content = document.createElement('div');
      content.style.padding = '16px';

      // 将原始内容移动到内容区域
      while (component.htmlElement.firstChild) {
        content.appendChild(component.htmlElement.firstChild);
      }

      // 添加标题栏和内容区域
      component.htmlElement.appendChild(titleBar);
      component.htmlElement.appendChild(content);
    }

    return component;
  }

  /**
   * 创建进度条
   * @param entity 实体
   * @param value 进度值（0-100）
   * @param props 进度条属性
   * @returns 创建的进度条组件
   */
  createProgressBar(entity: Entity, value: number = 0, props: UIProgressBarComponentProps = {}): UIProgressBarComponent {
    // 合并默认属性
    const mergedProps: UIProgressBarComponentProps = {
      value,
      progressColor: props.progressColor || '#1890ff',
      trackColor: props.trackColor || '#f0f0f0',
      ...props
    };

    // 创建进度条组件
    const component = new UIProgressBarComponent(mergedProps);

    // 注册到UI系统
    this.uiSystem.registerUIComponent(entity, component);

    // 添加到容器
    if (this.container && component.htmlElement && !component.htmlElement.parentElement) {
      this.container.appendChild(component.htmlElement);
    }

    return component;
  }

  /**
   * 创建工具提示
   * @param entity 实体
   * @param content 提示内容
   * @param target 目标元素
   * @param props 工具提示属性
   * @returns 创建的工具提示组件
   */
  createTooltip(entity: Entity, content: string, target?: HTMLElement | UI2DComponent, props: UITooltipComponentProps = {}): UITooltipComponent {
    // 合并默认属性
    const mergedProps: UITooltipComponentProps = {
      content,
      target,
      theme: props.theme || 'dark',
      position: props.position || 'top',
      ...props
    };

    // 创建工具提示组件
    const component = new UITooltipComponent(mergedProps);

    // 注册到UI系统
    this.uiSystem.registerUIComponent(entity, component);

    // 添加到容器
    if (this.container && component.htmlElement && !component.htmlElement.parentElement) {
      this.container.appendChild(component.htmlElement);
    }

    return component;
  }

  /**
   * 创建模态对话框
   * @param entity 实体
   * @param title 标题
   * @param content 内容
   * @param props 模态框属性
   * @returns 创建的模态框组件
   */
  createModal(entity: Entity, title: string = '', content: string = '', props: UIModalComponentProps = {}): UIModalComponent {
    // 合并默认属性
    const mergedProps: UIModalComponentProps = {
      title,
      content,
      modalSize: props.modalSize || 'medium',
      modalType: props.modalType || 'default',
      ...props
    };

    // 创建模态框组件
    const component = new UIModalComponent(mergedProps);

    // 注册到UI系统
    this.uiSystem.registerUIComponent(entity, component);

    // 添加到容器
    if (this.container && component.htmlElement && !component.htmlElement.parentElement) {
      this.container.appendChild(component.htmlElement);
    }

    return component;
  }

  /**
   * 更新系统
   * @param _deltaTime 时间增量 - 未使用，因为UI系统会更新所有UI组件
   */
  update(_deltaTime: number): void {
    // 2D UI系统不需要额外的更新逻辑，因为UI系统会更新所有UI组件
  }

  /**
   * 渲染系统
   */
  render(): void {
    // 2D UI系统不需要额外的渲染逻辑，因为UI系统会渲染所有UI组件
  }

  /**
   * 销毁系统
   */
  dispose(): void {
    // 移除容器
    if (this.container && this.container.parentElement) {
      this.container.parentElement.removeChild(this.container);
    }

    this.container = undefined;
  }
}
